package com.jurassic.myhealth.login.data.gateway

import com.jurassic.myhealth.common.util.AppLogger
import com.jurassic.myhealth.login.domain.gateway.LoginGateway
import com.jurassic.myhealth.login.domain.model.Token
import dev.gitlive.firebase.auth.FirebaseAuth

class FirebaseLoginGateway(
    private val firebaseAuth: FirebaseAuth
) : LoginGateway {

    override suspend fun createAccount(email: String, password: String): Result<Unit> =
        runCatching {
            firebaseAuth.createUserWithEmailAndPassword(email, password)
                .user
            AppLogger.d("FirebaseLoginGateway", "Account created successfully")
        }.onFailure { error ->
            AppLogger.e("FirebaseLoginGateway", "Failed to create account", error)
        }

    override suspend fun login(email: String, password: String): Result<Unit> {
        val auth = firebaseAuth.signInWithEmailAndPassword(email, password)

        AppLogger.d("FirebaseLoginGateway", "Token:${auth.user?.getIdToken(true)}")

        return Result.success(Unit)
    }

    override suspend fun isUserLogIn(): Result<Boolean> =
        Result.success(firebaseAuth.currentUser != null)

    override suspend fun getToken(forceRefresh: Boolean): Result<Token> =
        firebaseAuth.currentUser?.getIdToken(forceRefresh)?.let { token ->
            Result.success(Token(token))
        } ?: Result.failure(Exception("User not authenticated"))
}