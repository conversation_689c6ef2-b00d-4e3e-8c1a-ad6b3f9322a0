package com.jurassic.myhealth.login.presentation.feature.signin.composable

import androidx.compose.material.SnackbarHostState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.navigation.NavController
import com.jurassic.myhealth.login.presentation.feature.signin.model.SignInContract.Effect
import com.jurassic.myhealth.login.presentation.feature.signin.viewmodel.SignInViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun SignInScreen(
    navController: NavController
) {
    val viewModel = koinViewModel<SignInViewModel>()

    LaunchedEffect(SIDE_EFFECTS_KEY) {
        viewModel.effect.onEach { effect ->
            when (effect) {
                Effect.NavigateToSignUp -> navController.navigate("sign_up")
                Effect.NavigateToHome -> navController.navigate("home")
                Effect.NavigateToAddProviders -> navController.navigate("add_providers")
            }
        }.collect()
    }

    SignInScreenContent(
        uiState = viewModel.uiState.value,
        onEmailChange = viewModel::onEmailChange,
        onPasswordChange = viewModel::onPasswordChange,
        onSignInClick = viewModel::onSignInClick,
        onForgotPasswordClick = viewModel::onForgotPasswordClick,
        onCreateAccountClick = viewModel::onCreateAccountClick,
    )
}

const val SIDE_EFFECTS_KEY = "side-effects_key"