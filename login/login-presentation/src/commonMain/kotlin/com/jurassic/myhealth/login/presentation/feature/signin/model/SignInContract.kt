package com.jurassic.myhealth.login.presentation.feature.signin.model

internal class SignInContract {

    data class UiState(
        val email: String = "",
        val password: String = "",
        val isLoading: Boolean = false
    )

    sealed class Effect  {
        data object NavigateToSignUp : Effect()
        data object NavigateToHome : Effect()
        data object NavigateToAddProviders : Effect()
        data class ShowErrorMessage(val message: String?) : Effect()
    }
}