# Android App Integration Guide

This document provides answers to Android app integration questions for the MyHealth Service backend.

## Server Status & Configuration

### ✅ Server Running Status
- **Status**: Server is currently running and listening on port 8080
- **Process**: Confirmed via system process check
- **Network**: Port 8080 is actively listening for connections

### ✅ LuxMed Sync API Endpoint
- **Endpoint Path**: `/api/v1/sync/luxmed`
- **HTTP Method**: POST
- **Content-Type**: `application/json`
- **Controller**: `LuxmedSyncController.syncLuxmedData()`

## Authentication & Headers

### ⚠️ Authorization Header Format
The server expects the standard Bearer token format:
```
Authorization: Bearer <firebase_jwt_token>
```

**Important Notes:**
- The endpoint requires a valid Firebase JWT token
- Currently marked as `permitAll()` in security config for testing purposes
- Controller still validates authentication and returns 401 if missing
- Authentication is injected as `AuthenticatedUserPrincipal?` parameter

### Request Body Format
The endpoint expects a JSON body with LuxMed authentication components:
```json
{
  "jwtToken": "jwt_token_from_authorization_header",
  "aspNetSessionId": "session_id_from_cookie",
  "lxToken": "lx_token_from_cookie",
  "refreshToken": "refresh_token_from_cookie",
  "userAdditionalInfo": "user_info_jwt_from_cookie",
  "xsrfToken": "xsrf_token_from_cookie",
  "incapsulaSessionId": "optional_incapsula_session",
  "deviceId": "optional_device_id"
}
```

## CORS Configuration

### ❌ CORS Currently Disabled
- CORS is disabled in the security configuration
- This should work fine for Android apps (native HTTP clients)
- Web browsers from localhost/emulator should also work
- If needed for web clients, CORS can be enabled in `SecurityConfig`

## Health Check Endpoints

### ✅ Available Health Check Endpoints

1. **Spring Boot Actuator Health**
   ```
   GET /actuator/health
   Response: {"status":"UP"}
   ```

2. **Public Sample Endpoint**
   ```
   GET /api/v1/sample/public
   Response: {"message":"This is a public endpoint"}
   ```

## Testing Commands

### Basic Health Check
```bash
curl http://localhost:8080/actuator/health
```

### Public Endpoint Test
```bash
curl http://localhost:8080/api/v1/sample/public
```

### LuxMed Sync Endpoint Test
```bash
curl -X POST http://localhost:8080/api/v1/sync/luxmed \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_FIREBASE_TOKEN" \
  -d '{
    "jwtToken": "your_jwt_token",
    "aspNetSessionId": "your_session_id",
    "lxToken": "your_lx_token",
    "refreshToken": "your_refresh_token",
    "userAdditionalInfo": "your_user_info",
    "xsrfToken": "your_xsrf_token"
  }'
```

## Response Formats

### Success Response
```json
{
  "success": true,
  "message": "LuxMed data synchronized successfully"
}
```

### Error Responses

**Authentication Required (401)**
```json
{
  "success": false,
  "message": "Authentication required for LuxMed synchronization"
}
```

**Invalid Request Data (400)**
```json
{
  "success": false,
  "message": "Invalid LuxMed authentication components"
}
```

**Server Error (500)**
```json
{
  "success": false,
  "message": "Failed to synchronize LuxMed data: [error details]"
}
```

## Integration Checklist for Android App

- [x] **Server Running**: localhost:8080
- [x] **Endpoint Path**: `/api/v1/sync/luxmed`
- [x] **HTTP Method**: POST
- [x] **Content-Type**: `application/json`
- [x] **Auth Header**: `Authorization: Bearer <firebase_token>`
- [x] **CORS Support**: Available (disabled = permissive)
- [x] **Health Check**: `/actuator/health` or `/api/v1/sample/public`

## Additional API Endpoints

### Medical Data Retrieval
- `GET /api/v1/data/lab-results` - Get lab results (requires auth)
- `GET /api/v1/data/visits` - Get visits (requires auth)  
- `GET /api/v1/data/all` - Get all medical data (requires auth)

### Authentication Endpoints
- `POST /api/v1/users/register` - User registration (public)
- `GET /api/v1/users/me` - Get current user profile (requires auth)

## Notes

1. **Firebase Authentication**: Ensure your Android app obtains a valid Firebase JWT token before making requests
2. **Error Handling**: Implement proper error handling for 401, 400, and 500 responses
3. **Testing**: Start with health check endpoints to verify connectivity
4. **Security**: The LuxMed endpoint is temporarily public for testing - this may change in production

---

*Generated on: 2025-01-22*
*Server Status: Running on port 8080*
