package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.composable

import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.navigation.NavController
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LoginProviderContract
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.viewmodel.LoginProviderViewModel
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun LoginProviderScreen(
    navController: NavController,
    onShowMessage: (String) -> Unit = {}
) {
    val viewModel = koinViewModel<LoginProviderViewModel>()

    LaunchedEffect(SIDE_EFFECTS_KEY) {
        viewModel.effect.onEach { effect ->
            when (effect) {
                LoginProviderContract.Effect.NavigateToHome -> {
                    navController.navigate("home") {
                        popUpTo("login_provider") { inclusive = true }
                    }
                }
                is LoginProviderContract.Effect.ShowMessage -> {
                    onShowMessage(effect.message)
                }
            }
        }.collect()
    }

    LoginProviderScreenContent(
        uiState = viewModel.uiState.value,
        onTokenReceived = viewModel::onAuthenticationTokenReceived,
        onTestConnection = { onShowMessage("Test connection clicked - check logs") }
    )
}

const val SIDE_EFFECTS_KEY = "side-effects_key"