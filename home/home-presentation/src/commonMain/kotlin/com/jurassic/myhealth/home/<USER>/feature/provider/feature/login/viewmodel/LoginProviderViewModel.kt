package com.jurassic.myhealth.home.presentation.feature.provider.feature.login.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.jurassic.myhealth.common.util.AppLogger
import com.jurassic.myhealth.home.domain.interactors.SyncLuxmedDataUseCase
import com.jurassic.myhealth.home.domain.repository.HealthDataRepository
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.mapper.LuxmedAuthMapper
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LoginProviderContract
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.LuxMedAuthentication
import com.jurassic.myhealth.home.presentation.feature.provider.feature.login.model.SyncState
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.launch

class LoginProviderViewModel(
    private val syncLuxmedDataUseCase: SyncLuxmedDataUseCase,
    private val luxmedAuthMapper: LuxmedAuthMapper,
    private val healthDataRepository: HealthDataRepository
) : ViewModel() {

    private val _effect: Channel<LoginProviderContract.Effect> = Channel()

    val effect = _effect.receiveAsFlow()
    var uiState = mutableStateOf(LoginProviderContract.UiState())
        private set

    fun testServerConnection() {
        viewModelScope.launch {
            AppLogger.d("LoginProviderViewModel", "Testing server connection...")
            _effect.trySend(LoginProviderContract.Effect.ShowMessage("Testing connection..."))

            healthDataRepository.testConnection()
                .onSuccess { response ->
                    AppLogger.d("LoginProviderViewModel", "Connection test successful: $response")
                    _effect.trySend(LoginProviderContract.Effect.ShowMessage("✅ Connection successful: $response"))
                }
                .onFailure { error ->
                    AppLogger.e("LoginProviderViewModel", "Connection test failed", error)
                    _effect.trySend(LoginProviderContract.Effect.ShowMessage("❌ Connection failed: ${error.message}"))
                }
        }
    }

    fun onAuthenticationTokenReceived(authenticationToken: LuxMedAuthentication) {
        AppLogger.d("LoginProviderViewModel", "Authentication: $authenticationToken")
        syncLuxmedData(authenticationToken)
    }

    private fun syncLuxmedData(authenticationToken: LuxMedAuthentication) {
        viewModelScope.launch {
            uiState.value = uiState.value.copy(syncState = SyncState.Loading)

            syncLuxmedDataUseCase.execute(luxmedAuthMapper.toDomain(authenticationToken))
                .onSuccess {
                    uiState.value = uiState.value.copy(syncState = SyncState.Success("Sync successful"))
                    _effect.trySend(LoginProviderContract.Effect.ShowMessage("Sync successful"))
                    _effect.trySend(LoginProviderContract.Effect.NavigateToHome)
                }
                .onFailure { error ->
                    val errorMessage = error.message ?: "Unknown error occurred"
                    uiState.value = uiState.value.copy(syncState = SyncState.Error(errorMessage))
                    _effect.trySend(LoginProviderContract.Effect.ShowMessage("Sync failed: $errorMessage"))
                    AppLogger.e("LoginProviderViewModel", "Sync failed", error)
                }
        }
    }
}