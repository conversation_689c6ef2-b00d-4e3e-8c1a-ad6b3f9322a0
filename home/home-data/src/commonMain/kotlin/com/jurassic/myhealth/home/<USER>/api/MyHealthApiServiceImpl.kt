package com.jurassic.myhealth.home.data.api

import com.jurassic.myhealth.home.data.model.LuxmedSyncRequest
import com.jurassic.myhealth.home.data.model.SynchronizationResponse
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType

class MyHealthApiServiceImpl(
    private val httpClient: HttpClient,
    private val baseUrl: String = "http://********:8080" // Android emulator host
) : MyHealthApiService {

    override suspend fun syncLuxmedData(
        authorization: String,
        request: LuxmedSyncRequest
    ): Result<SynchronizationResponse> =
        runCatching {
            val response = httpClient.post("$baseUrl/api/v1/sync/luxmed") {
                header(HttpHeaders.Authorization, authorization)
                contentType(ContentType.Application.Json)
                setBody(request)
            }

            response.body<SynchronizationResponse>()
        }
}